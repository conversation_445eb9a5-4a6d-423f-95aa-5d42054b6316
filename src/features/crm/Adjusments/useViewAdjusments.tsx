import { useEffect, useState, useRef, useCallback } from "react";
import {
  IAdjustmentRequest,
  IAdjustmentDetailRequest,
  IUpdateAdjustmentRequest,
  IUpdateAdjustmentDetailRequest,
  IUpdateAdjustmentPathParams,
  IUpdateAdjustmentQuery,
  IGetAdjustmentQuery,
  IAdjustmentResponseDTO,
} from "@modules/adjustment/interfaces/payloads/IAdjustmentPayload";
import { useSnackBar } from "@common";
import { getErrorToDisplay } from "itsf-ui-common";
import { createAdjustment, getAdjustment, updateAdjustment } from "@modules/adjustment/apis/apis";
import { ICreateCreditNoteRequest } from "@modules/financial-document/interfaces/payloads";
import {
  IElligibleInvoiceResponse,
  IPageResponseHelperCreditNoteViewResponse,
} from "@modules/financial-document/interfaces/responses/ICreditNotesResponses";
import { createCreditNote, getEligibleInvoices, getPaginatedNotes } from "@modules/financial-document/apis/v0";
import { getConfig } from "@config";
import { useUser } from "@hooks/useUser";
import { useAuth } from "@hooks/useAuth";
import { useTranslation } from "react-i18next";
import { updateCase, getCaseById, createCaseNote, getCasesByAccountId } from "@modules/caseManagement/apis/v1";
import { IUpdateCaseDTO, ICreateNoteDTO } from "@modules/caseManagement/interfaces/payloads";


interface IPropsViewAdjusments {
  accountId: string;
}

// Constantes para mejorar la mantenibilidad
const ADJUSTMENT_TYPE_ID_CREDIT_NOTE = 13;
const DEFAULT_USER = "CRM-PROVISIONAL";
const RETRY_ATTEMPTS = 3;
const RETRY_BASE_DELAY = 150;

export const useViewAdjustments = ({ accountId, }: IPropsViewAdjusments) => {
  const { setSnackBarError } = useSnackBar();
  const { t } = useTranslation(["customer"]);

  // Obtener el valor maximo aceptable para auto-aprobar una nota de crédito
  const maxAcceptableAmountAutoApprovalCreditNoteCents = getConfig().maxAcceptableAmountAutoApprovalCreditNoteCents;

  // Verificar si el usuario tiene el rol SUPERVISOR en galaxion-adjustments
  const { userHasRoles: canApprove } = useAuth({
    permissionType: "galaxion-adjustments",
    roles: ["SUPERVISOR"]
  });

  // Estado para almacenar los adjustments obtenidos automáticamente
  const [adjustmentsList, setAdjustmentsList] = useState<IAdjustmentResponseDTO[]>([]);
  const [isLoadingAdjustments, setIsLoadingAdjustments] = useState<boolean>(false);
  const [caseVerified, setCaseVerified] = useState<boolean>(false);

  const user = useUser();

  // Referencias para el intervalo de monitoreo
  const intervalIdRef = useRef<NodeJS.Timeout | null>(null);
  const lastAdjustmentsHashRef = useRef<string>("");
  

  // Estado para almacenar las facturas elegibles
  const [eligibleInvoices, setEligibleInvoices] = useState<IElligibleInvoiceResponse[]>([]);

  // Variable para manejar el estado del caseId
  const [caseId, setCaseId] = useState<number>(0);

  /// ***** HELPERS ***** ///
  const nowLocalForLocalDateTime = () => {
    const d = new Date();
    const local = new Date(d.getTime() - d.getTimezoneOffset() * 60000);
    return local.toISOString().slice(0, -1); // remueve la Z
  };

  // Helper para logging mejorado con contexto
  const logError = (context: string, error: unknown, additionalData?: Record<string, any>) => {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(`[${context}] Error:`, errorMessage, additionalData ? { ...additionalData } : '');
  };

  // Helper para generar hash de adjustments para detectar cambios
  const generateAdjustmentsHash = useCallback((adjustments: IAdjustmentResponseDTO[]): string => {
    const adjustmentsData = adjustments.map(adj => ({
      id: adj.id,
      approvedBy: adj.approvedBy,
      approvedDate: adj.approvedDate,
      deniedBy: adj.deniedBy,
      deniedDate: adj.deniedDate,
      invoiceId: adj.invoiceId,
      totalAmount: adj.totalAmount,
      detailsCount: adj.detail?.length || 0,
      detailsStatus: adj.detail?.map(d => d.status).join(',') || ''
    }));
    return JSON.stringify(adjustmentsData);
  }, []);

  // Helper para verificar si hay adjustments con status approved
  const hasApprovedAdjustments = useCallback((adjustments: IAdjustmentResponseDTO[]): boolean => {
    return adjustments.some(adj => adj.approvedBy && adj.approvedDate && adj.detail?.some(d => d.status === "approved"));
  }, []);

  // Retry simple con backoff para manejar deadlocks/transient errors
  async function withRetry<T>(fn: () => Promise<T>, attempts = RETRY_ATTEMPTS) {
    let lastErr: unknown;
    for (let i = 0; i < attempts; i++) {
      try {
        return await fn();
      } catch (e: any) {
        lastErr = e;
        const msg = (e?.message || "").toLowerCase();
        const retriable =
          msg.includes("deadlock") ||
          msg.includes("cannotacquirelock") ||
          msg.includes("sqltransactionrollback");
        if (!retriable || i === attempts - 1) throw e;
        await new Promise((r) => setTimeout(r, RETRY_BASE_DELAY * Math.pow(2, i)));
      }
    }
    throw lastErr;
  }

  /// ***** METODOS PRIMARIOS PARA HACER UN CRUD DE ADJUSTMENT ****///
  const callAdjusmentCustomServiceCreateAdjusment = async ({
    invoiceId,
    totalAmount,
    createdBy,
    caseId,
    orderId,
    adjustmentTypeId,
    details,
  }: {
    invoiceId: number;
    totalAmount: number;
    createdBy: string;
    caseId: number;
    orderId: string;
    adjustmentTypeId: number;
    details: IAdjustmentDetailRequest[];
  }): Promise<number> => {
    try {
      const createAdjusmentPayload: IAdjustmentRequest = {
        invoiceId,
        accountId,
        totalAmount,
        createdBy,
        caseId,
        orderId,
        adjustmentTypeId,
        detail: details,
      };
      const result = await createAdjustment(createAdjusmentPayload);
      
      if (result) {
        return result.adjustmentId ?? 0;
      }else{
        return 0;
      }
    } catch (error) {
      setSnackBarError(getErrorToDisplay(error));
      return 0;
    }
  };

  const callAdjusmentCustomServiceUpdateAdjusment = async ({
    adjustmentId,
    idRubro,
    totalAmount,
    deductSerialNo,
    orderId,
    adjustmentTypeId,
    deniedBy,
    deniedDate,
    approvedBy,
    approvedDate,
    details,
  }: {
    adjustmentId: number;
    idRubro?: number;
    totalAmount?: number;
    deductSerialNo?: string;
    orderId?: string;
    adjustmentTypeId?: number;
    deniedBy?: string;
    deniedDate?: string;
    approvedBy?: string;
    approvedDate?: string;
    details?: IUpdateAdjustmentDetailRequest[];
  }): Promise<boolean> => {
    try {
      const params: IUpdateAdjustmentPathParams = { adjustmentId };
      const query: IUpdateAdjustmentQuery = { idRubro };

      const updateAdjusmentPayload: IUpdateAdjustmentRequest = {
        totalAmount,
        deductSerialNo,
        orderId,
        adjustmentTypeId,
        deniedBy,
        deniedDate,
        approvedBy,
        approvedDate,
        detail: details,
      };

      await updateAdjustment(params, updateAdjusmentPayload, query);
      return true;
    } catch (error) {
      setSnackBarError(getErrorToDisplay(error));
      return false;
    }
  };

  const callAdjusmentCustomServiceGetAdjusment = async ({
    adjustmentId,
    invoiceId,
    caseId,
    status,
  }: {
    adjustmentId?: number | string;
    invoiceId?: number | string;
    caseId?: number | string;
    status?: string;
  }): Promise<IAdjustmentResponseDTO | null> => {
    try {
      const query: IGetAdjustmentQuery = {
        ...(adjustmentId != null ? { adjustmentId } : {}),
        ...(invoiceId != null ? { invoiceId } : {}),
        ...(caseId != null ? { caseId } : {}),
        ...(status != null ? { status } : {}),
      } as IGetAdjustmentQuery;

      const response = await getAdjustment(query);
      if(response?.id){
        return response;
      }else{
        return null;
      }
    } catch (error) {
      setSnackBarError(getErrorToDisplay(error));
      return null;
    }
  };

  /// ***** METODOS PRIMARIOS PARA HACER UN CRUD DE FINANCIAL DOCUMENTS (CREATE, SELECT) ****///
  const callFinancialDocumentsCreateCreditNote = async ({
    invoiceNumber,
    adjustmentType,
    comment,
    services,
  }: ICreateCreditNoteRequest): Promise<boolean> => {
    try {
      await createCreditNote({
        accountId,
        invoiceNumber,
        adjustmentType,
        comment,
        services,
      });
      return true;
    } catch (error) {
      setSnackBarError(getErrorToDisplay(error));
      return false;
    }
  };

  /// ***** METODOS PRIMARIOS PARA CASE MANAGEMENT ****///
  const callUpdateCase = async (caseId: number, payload: IUpdateCaseDTO): Promise<boolean> => {
    try {
      await updateCase(caseId, payload);
      return true;
    } catch (error) {
      logError("callUpdateCase", error, { caseId, payload });
      setSnackBarError(getErrorToDisplay(error));
      return false;
    }
  };

  const callGetCaseById = async (caseId: number): Promise<boolean> => {
    try {
      const response = await getCaseById(caseId);
      return response ? true : false;
    } catch (error) {
      logError("callGetCaseById", error, { caseId });
      // No mostrar error en snackbar para este caso específico
      return false;
    }
  };

  const callCreateCaseNote = async (caseId: number, content: string): Promise<boolean> => {
    try {
      const payload: ICreateNoteDTO = {
        content,
        userId: user?.username ?? DEFAULT_USER,
        requestedBy: user?.username ?? DEFAULT_USER,
        caseId,
      };
      await createCaseNote(caseId, payload);
      return true;
    } catch (error) {
      logError("callCreateCaseNote", error, { caseId, content });
      setSnackBarError(getErrorToDisplay(error));
      return false;
    }
  };

  const callFinancialDocumentsGetPaginatedNotes = async (): Promise<IPageResponseHelperCreditNoteViewResponse | null> => {
    try {
      const response = await getPaginatedNotes({ accountId });
      return response ?? null;
    } catch (error) {
      logError("callFinancialDocumentsGetPaginatedNotes", error, { accountId });
      // No mostrar error en snackbar para el monitoreo automático
      return null;
    }
  };



  const callFinancialDocumentsGetEligibleInvoices = async (): Promise<IElligibleInvoiceResponse[] | null> => {
    try {
      const response = await getEligibleInvoices({ accountId });
      return response ?? null;
    } catch (error) {
      setSnackBarError(getErrorToDisplay(error));
      return null;
    }
  };

  // Método que se ejecuta automáticamente al crear la instancia
  const loadAdjustmentsFromEligibleInvoices = async (): Promise<void> => {

    if (!accountId) return;

    setIsLoadingAdjustments(true);
    try {
      // Mantener la llamada a getEligibleInvoices para no causar errores en otros elementos
      const fetchedEligibleInvoices = await callFinancialDocumentsGetEligibleInvoices();

      if (!fetchedEligibleInvoices || fetchedEligibleInvoices.length === 0) {
        setAdjustmentsList([]);
        setEligibleInvoices([]);
        return;
      }

      setEligibleInvoices(fetchedEligibleInvoices);

      // Obtener los cases de la cuenta actual
      const casesResponse = await getCasesByAccountId(accountId);
      //console.log("casesResponse", casesResponse);

      // Extraer los caseIds del contenido de la respuesta paginada
      let caseIds: number[] = [];
      if (casesResponse && casesResponse.content && Array.isArray(casesResponse.content)) {
        caseIds = casesResponse.content
          .map((caseItem) => caseItem.id)
          .filter((id): id is number => id != null);
      }

      //console.log("caseIds extraídos:", caseIds);

      if (caseIds.length === 0) {
        setAdjustmentsList([]);
        return;
      }

      // Buscar adjustments por caseId en lugar de invoiceId
      const adjustmentsPromises = caseIds.map(async (caseId) => {
        try {
          const res = await callAdjusmentCustomServiceGetAdjusment({ caseId });
          if (Array.isArray(res)) return res;
          return res ?? null;
        } catch (error) {
          logError("loadAdjustmentsFromEligibleInvoices", error, { caseId });
          return null;
        }
      });

      const rawResults = await Promise.all(adjustmentsPromises);
      const flatResults: unknown[] = rawResults.flatMap((item) => (Array.isArray(item) ? item : [item]));
      const validAdjustments: IAdjustmentResponseDTO[] = flatResults.filter((adj): adj is IAdjustmentResponseDTO =>
        isAdjustmentResponseDTO(adj),
      );

      setAdjustmentsList(validAdjustments);
    } catch (error) {
      //console.error("Error en loadAdjustmentsFromEligibleInvoices:", error);
      setSnackBarError(getErrorToDisplay(error));
      setAdjustmentsList([]);
    } finally {
      setIsLoadingAdjustments(false);
    }
  };

  // --- Type guards ---
  const isAdjustmentDetail = (x: unknown): x is {
    adjustmentId: number;
    idRubro: number;
    amount: number;
    status: string;
    disputeSerialNo?: string;
    adjustmentSerialNo?: string;
  } => {
    if (!x || typeof x !== "object") return false;
    const o = x as any;
    return (
      typeof o.adjustmentId === "number" &&
      typeof o.idRubro === "number" &&
      typeof o.amount === "number" &&
      typeof o.status === "string"
    );
  };

  const isAdjustmentResponseDTO = (x: unknown): x is IAdjustmentResponseDTO => {
    if (!x || typeof x !== "object") return false;
    const o = x as any;
    return (
      typeof o.id === "number" &&
      typeof o.invoiceId === "number" &&
      typeof o.accountId === "string" &&
      typeof o.totalAmount === "number" &&
      Array.isArray(o.detail) &&
      o.detail.every(isAdjustmentDetail) &&
      typeof o.type === "string"
    );
  };

  // useEffect para ejecutar el método automáticamente al crear la instancia
  useEffect(() => {
    loadAdjustmentsFromEligibleInvoices();
  }, [accountId]);



  /**
   * Actualiza los adjustmentSerialNo de cada detalle con el UUID original
   */
  const updateAdjustmentWithSerialNumbers = async (
    adjustmentId: number,
    totalAmount: number,
    adjustmentTypeId: number,
    detailsWithUuid: Array<{ convertedId: number; originalUuid: string; amount: number }>
  ): Promise<boolean> => {
    try {
      // Actualizar cada detalle con su adjustmentSerialNo correspondiente
      for (const { convertedId, originalUuid, amount } of detailsWithUuid) {
        const detailUpdate: IUpdateAdjustmentDetailRequest = {
          status: "init", // Estado inicial para detalles recién creados
          amount: amount, // Amount correcto del detalle
          adjustmentSerialNo: originalUuid, // UUID original antes de conversión
        };

        await withRetry(() =>
          callAdjusmentCustomServiceUpdateAdjusment({
            adjustmentId,
            totalAmount,
            adjustmentTypeId,
            idRubro: convertedId,
            details: [detailUpdate],
          })
        );
      }

      return true;
    } catch (error) {
      logError("updateAdjustmentWithSerialNumbers", error, {
        adjustmentId,
        totalAmount,
        adjustmentTypeId,
        detailsCount: detailsWithUuid.length
      });
      setSnackBarError(getErrorToDisplay(error));
      return false;
    }
  };

  // Método para crear credit note usando adjustment
  const createCreditNoteFromAdjustment = async ({
    invoiceId,
    details,
  }: {
    invoiceId: number;
    details: { idRubro: string; amount: number }[];
  }): Promise<number> => {
    try {
      const totalAmount = details.reduce((sum, detail) => sum + detail.amount, 0);

      // Crear mapeo completo con toda la información necesaria
      const detailsWithUuid: Array<{ convertedId: number; originalUuid: string; amount: number }> = [];

      const adjustmentDetails: IAdjustmentDetailRequest[] = details.map((detail) => {
        const convertedId = uuidToJsonSafePositiveNumber(detail.idRubro);

        // Guardar información completa para la actualización posterior
        detailsWithUuid.push({
          convertedId,
          originalUuid: detail.idRubro,
          amount: detail.amount
        });

        return {
          idRubro: convertedId,
          amount: detail.amount,
        };
      });

      const result = await callAdjusmentCustomServiceCreateAdjusment({
        invoiceId,
        totalAmount,
        createdBy: user?.username ?? DEFAULT_USER,
        caseId,
        orderId: "",
        adjustmentTypeId: ADJUSTMENT_TYPE_ID_CREDIT_NOTE,
        details: adjustmentDetails,
      });

      if (result > 0) {
        // Actualizar cada detalle con su adjustmentSerialNo (UUID original)
        const updateSuccess = await updateAdjustmentWithSerialNumbers(
          result,
          totalAmount,
          ADJUSTMENT_TYPE_ID_CREDIT_NOTE,
          detailsWithUuid
        );

        if (!updateSuccess) {
          console.warn(t("customer:adjustmentCreatedButNotUpdated"));
        }

        // Llamar updateCase cuando se ejecuta createCreditNoteFromAdjustment
        if (caseId > 0) {
          const updateCasePayload: IUpdateCaseDTO = {
            id: caseId,
            adjustmentId: result.toString(),
            adjustmentType: "CREDIT",
            requestedBy: user?.username ?? DEFAULT_USER,
          };
          await callUpdateCase(caseId, updateCasePayload);
        }

        // Esperar a que se actualice la lista antes de retornar
        await loadAdjustmentsFromEligibleInvoices();
        return result;
      } else {
        return 0;
      }
    } catch (error) {
      logError("createCreditNoteFromAdjustment", error, { invoiceId, detailsCount: details.length });
      setSnackBarError(getErrorToDisplay(error));
      return 0;
    }
  };

  // Método para verificar caso (botón verify)
  const verifyCaseById = async (caseId: number): Promise<void> => {
    try {
      const isValid = await callGetCaseById(caseId);

      if (isValid) {
        // VALIDACION 1: Verificar si el caseId ya fue usado en otro adjustment
        const existingAdjustment = await callAdjusmentCustomServiceGetAdjusment({ caseId });

        //console.log("existingAdjustment:", existingAdjustment);

        if (existingAdjustment) {
          setSnackBarError("Este caso ya fue usado en otro adjustment");
          setCaseVerified(false);
          return;
        }else{
          setCaseVerified(true);
          return;
        }
      }else{
          setSnackBarError("El número de caso no es válido");
          setCaseVerified(false);
          return;
      }
    } catch (error) {
      logError("verifyCaseById", error, { caseId });
      setCaseVerified(false);
    }
  };

  /**
   * Actualiza el adjustment y sus detalles según el estatus:
   * 1) Actualiza CABECERA primero (una sola vez).
   * 2) Luego actualiza cada DETALLE en SERIE (sin Promise.all).
   * 3) Con retry ante deadlock/transient errors.
   */
  const updateAdjustmentStatus = async ({
    adjustmentId,
    status,
    adjustment,
  }: {
    adjustmentId: number;
    status: "approved" | "denied" | "dispute" | "completed";
    adjustment: IAdjustmentResponseDTO;
  }): Promise<boolean> => {
    try {
      const currentDate = nowLocalForLocalDateTime();

      if (!adjustment.detail || adjustment.detail.length === 0) {
        throw new Error(t("customer:noAdjustmentDetails"));
      }


      // 1) DETALLES en serie
      for (const detail of adjustment.detail) {
        if (!detail.idRubro) {
          throw new Error(`Detalle sin idRubro encontrado en adjustment ${adjustmentId}`);
        }

        const detailUpdate: IUpdateAdjustmentDetailRequest = {
          status,
          amount: detail.amount,
          disputeSerialNo: detail.disputeSerialNo,
          adjustmentSerialNo: detail.adjustmentSerialNo,
        };

        await withRetry(() =>
          callAdjusmentCustomServiceUpdateAdjusment({
            adjustmentId,
            adjustmentTypeId: adjustment.adjustmentTypeId,
            totalAmount: adjustment.totalAmount,
            approvedBy:adjustment.approvedBy,
            approvedDate: adjustment.approvedDate,
            deniedBy: adjustment.deniedBy,
            deniedDate: adjustment.deniedDate,
            deductSerialNo: adjustment.deductSerialNo,
            idRubro: detail.idRubro,
            details: [detailUpdate],
          }),
        );
      }

      // 2) CABECERA
      if (status === "approved"|| status === "denied") {
        await withRetry(() =>
            callAdjusmentCustomServiceUpdateAdjusment({
            adjustmentId,
            adjustmentTypeId: adjustment.adjustmentTypeId,
            totalAmount: adjustment.totalAmount,
            approvedBy: status === "approved" ? user?.username ?? DEFAULT_USER : undefined,
            approvedDate: status === "approved" ? currentDate : undefined,
            deniedBy: status === "denied" ? user?.username ?? DEFAULT_USER : undefined,
            deniedDate: status === "denied" ? currentDate : undefined,
            deductSerialNo: status === "approved" ? "SENT": undefined,
            // sin idRubro, sin detail
            }),
        );

        // 3) LLAMADAS ADICIONALES SEGÚN EL ESTATUS
        if (caseId > 0) {
          // Crear nota del caso para ambos estados (approved y denied)
          const noteContent = status === "approved"
            ? `Adjustment ${adjustmentId} has been approved by ${user?.username ?? DEFAULT_USER}`
            : `Adjustment ${adjustmentId} has been denied by ${user?.username ?? DEFAULT_USER}`;

          await callCreateCaseNote(caseId, noteContent);

          // Si es approved, también crear credit note
          if (status === "approved") {
            // Preparar datos para crear credit note
            const services = adjustment.detail
              ?.filter(detail => detail.amount !== undefined)
              ?.map(detail => ({
                amount: detail.amount!,
                sectionId: detail.adjustmentSerialNo || "",
              })) || [];

            const invoiceNumber =
              adjustment.invoiceId != null && adjustment.invoiceId.toString() !== ""
                ? adjustment.invoiceId.toString().padStart(10, "0")
                : "";

            if (services.length > 0) {
              await callFinancialDocumentsCreateCreditNote({
                accountId,
                invoiceNumber: invoiceNumber,
                adjustmentType: "AJ",
                comment: `Credit note created from approved adjustment ${adjustmentId}`,
                services,
              });
            }
          }
        }
      }

      await loadAdjustmentsFromEligibleInvoices();
      return true;
    } catch (error) {
      //console.error("Error actualizando adjustment:", error);
      setSnackBarError(getErrorToDisplay(error));
      return false;
    }
  };

  // Helper mínimo: compara sets de strings
  const sameStringSet = (a: string[], b: string[]): boolean => {
    if (a.length !== b.length) return false;
    const setA = new Set(a);
    for (const x of b) if (!setA.has(x)) return false;
    return true;
  };

  // Función para procesar y comparar adjustments con financial documents
  const processFinancialDocumentsData = useCallback(async (
    financialData: IPageResponseHelperCreditNoteViewResponse,
    currentAdjustments: IAdjustmentResponseDTO[]
  ): Promise<void> => {
    const notes = financialData?.content ?? [];
    if (notes.length === 0) return;

    for (const adjustment of currentAdjustments) {
      try {
        //console.log("Procesando adjustment:", adjustment);

        const adjLines = adjustment.detail?.map(d => d.adjustmentSerialNo).filter(Boolean) as string[] | undefined;
        if (!adjustment.accountId || !adjLines || adjLines.length === 0) continue;

        // Buscar la primera coincidencia por accountId + set de líneas
        const matchingNote = notes.find(n =>
          n?.accountId === adjustment.accountId &&
          Array.isArray(n?.services) &&
          sameStringSet(
            n.services!.map(s => s.sectionId).filter((id): id is string => Boolean(id)),
            adjLines
          )
        );

        //console.log("Coincidencia encontrada:", matchingNote);

        const isTerminal = matchingNote &&
          (matchingNote.status === "VALIDATED" || matchingNote.status === "REJECTED"|| matchingNote.status === "SENT");
        const alreadyCompleted = adjustment.detail?.[0]?.status === "completed";

        //console.log("matchingNote.status:", matchingNote?.status);
        //console.log("alreadyCompleted:", alreadyCompleted);
        //console.log("isTerminal:", isTerminal);


        if (matchingNote && isTerminal && !alreadyCompleted) {

          //console.log("Actualizando adjustment a completed:", adjustment);

          // 1. Crear case note si hay caseId válido
          if ((adjustment.caseId as number) > 0) {
            const noteContent =
              `Estado: ${matchingNote.status}\n` +
              `Comentario: ${matchingNote.comment || "N/A"}\n` +
              `Motivo: ${matchingNote.reason || "N/A"}`;

            await callCreateCaseNote(adjustment.caseId!, noteContent);
          }

          // 2. Actualizar el status del adjustment a completed
          if (adjustment.id) {
            //console.log("Actualizando adjustment a completed 1 :", adjustment);
            adjustment.deductSerialNo = matchingNote.status;
            //console.log("Actualizando adjustment a completed 2 :", adjustment);
            await updateAdjustmentStatus({
              adjustmentId: adjustment.id,
              status: "completed",
              adjustment
            });
          }
        }
      } catch (error) {
        logError("processFinancialDocumentsData", error, {
          adjustmentId: adjustment.id,
          invoiceId: adjustment.invoiceId,
        });
      }
    }
  }, [callCreateCaseNote, updateAdjustmentStatus, logError]);


  // Función principal de monitoreo que se ejecuta cada minuto
  const startAdjustmentMonitoring = useCallback(() => {
    // Limpiar intervalo existente si hay uno
    if (intervalIdRef.current) {
      clearInterval(intervalIdRef.current);
    }

    intervalIdRef.current = setInterval(async () => {
      try {
        // Verificar si hay adjustments approved
        if (!hasApprovedAdjustments(adjustmentsList)) {
          return;
        }

        // Verificar si los adjustments han cambiado
        const currentHash = generateAdjustmentsHash(adjustmentsList);
        if (currentHash === lastAdjustmentsHashRef.current) {
          // No hay cambios, continuar con el monitoreo normal
        } else {
          // Hay cambios, actualizar el hash
          lastAdjustmentsHashRef.current = currentHash;
        }

        // Llamar a financial documents
        const financialData = await callFinancialDocumentsGetPaginatedNotes();
        if (financialData) {
          await processFinancialDocumentsData(financialData, adjustmentsList);
        }
      } catch (error) {
        logError("startAdjustmentMonitoring", error);
      }
    }, 60000); // 60 segundos = 1 minuto
  }, [adjustmentsList, hasApprovedAdjustments, generateAdjustmentsHash, callFinancialDocumentsGetPaginatedNotes, processFinancialDocumentsData, logError]);

  // Función para detener el monitoreo
  const stopAdjustmentMonitoring = useCallback(() => {
    if (intervalIdRef.current) {
      clearInterval(intervalIdRef.current);
      intervalIdRef.current = null;
    }
  }, []);

  /**
   * Convierte un UUID (36 chars con guiones) a un number JSON-safe (0..2^53-1).
   * Determinista: usa los 64 bits bajos y los reduce a 53 bits.
   */
  function uuidToJsonSafePositiveNumber(uuid: string): number {
    const hex = uuid.replace(/-/g, "").toLowerCase();
    if (!/^[0-9a-f]{32}$/.test(hex)) throw new Error(t("customer:invalidUuid"));

    const big = BigInt("0x" + hex);           // 128 bits
    const low64 = BigInt.asUintN(64, big);    // 64 bits inferiores (sin signo)
    const val53 = low64 & ((1n << 53n) - 1n); // 0..2^53-1
    return Number(val53);                     // JSON number exacto
  }

  // useEffect para inicializar el monitoreo cuando hay adjustments approved
  useEffect(() => {
    if (adjustmentsList.length > 0 && hasApprovedAdjustments(adjustmentsList)) {
      startAdjustmentMonitoring();
    } else {
      stopAdjustmentMonitoring();
    }

    // Cleanup al desmontar el componente
    return () => {
      stopAdjustmentMonitoring();
    };
  }, [adjustmentsList, hasApprovedAdjustments, startAdjustmentMonitoring, stopAdjustmentMonitoring]);

  return {
    // Métodos existentes
    callAdjusmentCustomServiceGetAdjusment,
    callAdjusmentCustomServiceUpdateAdjusment,
    callAdjusmentCustomServiceCreateAdjusment,
    callFinancialDocumentsCreateCreditNote,
    callFinancialDocumentsGetPaginatedNotes,
    callFinancialDocumentsGetEligibleInvoices,

    // Métodos de Case Management
    callUpdateCase,
    callGetCaseById,
    callCreateCaseNote,
    verifyCaseById,

    // Estados y métodos expuestos
    adjustmentsList,
    isLoadingAdjustments,
    loadAdjustmentsFromEligibleInvoices,
    eligibleInvoices,
    caseId,
    setCaseId,
    createCreditNoteFromAdjustment,
    updateAdjustmentStatus,
    updateAdjustmentWithSerialNumbers,
    canApprove,
    caseVerified,
    setCaseVerified,
    maxAcceptableAmountAutoApprovalCreditNoteCents,

    // Métodos de monitoreo
    startAdjustmentMonitoring,
    stopAdjustmentMonitoring,
    processFinancialDocumentsData,
  };
};
