import { getMicroserviceEndpoint, InternalV1EndpointOptions } from "@api-routes/endpoints";
import { fetcher } from "@utils/fetcher";
import { IFetchOptions } from "itsf-ui-common";
import { IElligibleInvoiceResponse, IPageResponseHelperCreditNoteViewResponse } from "../interfaces/responses/ICreditNotesResponses";
import { ICreateCreditNoteRequest, IGetEligibleInvoicesPathParams, IGetPaginatedNotesPathParams } from "../interfaces/payloads";

const FD_ENDPOINT = "financial-documents/";
const FD_PRIVATE_ENDPOINT = getMicroserviceEndpoint(FD_ENDPOINT, {
    ...InternalV1EndpointOptions,
    useApiGateway: true,
});
const FD_PRIVATE_ENDPOINT_TEST = "http://localhost:13419/";


/**
* POST /api/v1/credit-note
* 201: Credit Note successfully created
*/
export const createCreditNote = (
    payload: ICreateCreditNoteRequest,
    options: IFetchOptions = {}
) => {
    //`${FD_PRIVATE_ENDPOINT_TEST}api/v1/credit-note`
    const url = new URL(`${FD_PRIVATE_ENDPOINT}credit-note`);
    return fetcher<void>(url.toString(), {
        method: "POST",
        body: payload,
        ...options,
    });
};


/**
* GET /api/v1/credit-note/{accountId}/paginated
* 200: IPageResponseHelperCreditNoteViewResponse
*/
export const getPaginatedNotes = (
    params: IGetPaginatedNotesPathParams,
    options: IFetchOptions = {}
) => {
    // `${FD_PRIVATE_ENDPOINT_TEST}api/v1/credit-note/${encodeURIComponent(params.accountId)}/paginated`
    const url = new URL(
        `${FD_PRIVATE_ENDPOINT}credit-note/${encodeURIComponent(params.accountId)}/paginated`
    );
    return fetcher<IPageResponseHelperCreditNoteViewResponse>(url.toString(), {
        method: "GET",
        ...options,
    });
};


/**
* GET /api/v1/invoice/eligible/{accountId}
* 200 -> IElligibleInvoiceResponse[] (últimas 5 facturas elegibles)
* 400/500 -> también devuelve el mismo schema según el swagger
*/
export const getEligibleInvoices = (
    params: IGetEligibleInvoicesPathParams,
    options: IFetchOptions = {}
) => {
    //`${FD_PRIVATE_ENDPOINT_TEST}api/v1/invoice/eligible/${encodeURIComponent(params.accountId)}`
    const url = new URL(
        `${FD_PRIVATE_ENDPOINT}invoice/eligible/${encodeURIComponent(params.accountId)}`
    );
    return fetcher<IElligibleInvoiceResponse[]>(url.toString(), {
        method: "GET",
        ...options,
    });
};