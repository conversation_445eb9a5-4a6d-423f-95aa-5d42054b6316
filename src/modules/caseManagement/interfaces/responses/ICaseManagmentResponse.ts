// ---------- Shared / Errors ----------
export interface IErrorResponse {
  errorCode?: number;        // int32
  errorMessage?: string;
  timestamp?: string;        // ISO date-time
}


export interface ILocalUserJoinDTO {
  id?: number;               // int64
  uuid?: string;
  name?: string;
}

export interface INoteDTO {
  id?: number;               // int64
  content?: string;
  createdAt?: string;        // ISO date-time
  user?: ILocalUserJoinDTO;
  requestedBy?: string;
}



// ---------- Case (GET/PATCH response) ----------
export interface IChannelJoinDTO {
  id?: number;               // int64
  name?: string;
}

export interface ITypeJoinDTO {
  id?: number;               // int64
  name?: string;
}

export interface ICaseSubtypeJoinDTO {
  id?: number;               // int64
  name?: string;
  caseType?: ITypeJoinDTO;
  cunApplicable?: boolean;
}

export interface IStatusJoinDTO {
  id?: number;               // int64
  name?: string;
  color?: string;
}

export interface IPriorityJoinDTO {
  id?: number;               // int64
  name?: string;
}

export interface IQueueJoinDTO {
  id?: number;               // int64
  name?: string;
  enabled?: boolean;
}

export interface ICloseReasonDTO {
  id?: number;               // int64
  name?: string;
}

export interface ICloseSubreasonDTO {
  id?: number;               // int64
  name?: string;
  closeReason?: ICloseReasonDTO;
}

export interface IClosureTypeJoinDTO {
  id?: number;               // int64
  name?: string;
}

export interface ICUNJoinDTO {
  id?: number;               // int64
  value?: string;
  creationDate?: string;     // ISO date-time
}

export interface IReasonJoinDTO {
  id?: number;               // int64
  name?: string;
  type?: string;
  allowTechnicalVisit?: boolean;
  allowCreditNote?: boolean;
  allowDebitNote?: boolean;
}

export interface ICaseDTO {
  id?: number;                           // int64
  title?: string;
  summary?: string;
  creationDate?: string;                 // ISO date-time
  resolutionDate?: string;               // ISO date-time
  lastModifiedDate?: string;             // ISO date-time
  expectedResolutionDate?: string;       // ISO date-time
  radicationDate?: string;               // ISO date-time
  customerPhone?: string;
  customerEmail?: string;
  customerName?: string;
  technicalVisitId?: string;
  adjustmentId?: string;
  adjustmentType?: string;
  global?: boolean;
  serviceId?: string;
  serviceName?: string;
  customerId?: string;
  accountNumber?: string;

  assignedUser?: ILocalUserJoinDTO;
  reportedChannel?: IChannelJoinDTO;
  caseSubtype?: ICaseSubtypeJoinDTO;
  status?: IStatusJoinDTO;
  priority?: IPriorityJoinDTO;
  queue?: IQueueJoinDTO;
  closureType?: IClosureTypeJoinDTO;
  closeSubreason?: ICloseSubreasonDTO;
  cun?: ICUNJoinDTO;
  reason?: IReasonJoinDTO;

  appealSubsidy?: boolean;
  assignedUserEmail?: string;
  contactIdType?: string;
  contactIdNumber?: string;
  notificationType?: string;
  contactName?: string;
  callId?: string;
  createdByEmail?: string;
  closedByEmail?: string;
  forceCloseRequired?: boolean;
}


export interface ICaseListDTO {
  id?: number;                       // int64
  radicationDate?: string;           // ISO date-time
  customerName?: string;
  global?: boolean;
  serviceName?: string;
  accountNumber?: string;

  caseSubtype?: ICaseSubtypeJoinDTO;
  status?: IStatusJoinDTO;
  queue?: IQueueJoinDTO;
  priority?: IPriorityListDTO;       
  reason?: IReasonJoinDTO;

  assignedUserEmail?: string;
  contactIdType?: string;
  contactIdNumber?: string;

  cun?: ICUNJoinDTO;
}

export interface IPriorityListDTO {
  id?: number;   // int64
  name?: string;
}

export interface IPageResponse<T> {
  content?: T[];
  totalPages?: number;
  totalElements?: number;
  number?: number;   // página actual
  size?: number;     // tamaño de página
  first?: boolean;
  last?: boolean;
  empty?: boolean;
}


export type ICaseListPageResponse = IPageResponse<ICaseListDTO>;